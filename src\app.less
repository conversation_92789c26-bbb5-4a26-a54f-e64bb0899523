.header-container {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 0 32px;
  height: 72px;
  line-height: 72px;
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  z-index: 1000;
  transition: all 0.3s ease-in-out;

  &:hover {
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.2) !important;
  }

  &.welcome-page {
    background: transparent;
    box-shadow: none;

    &.scrolled {
      background: rgba(255, 255, 255, 0.85);
      box-shadow: 0 2px 8px rgba(0, 0, 0, 0.2);
    }
  }

  &:not(.welcome-page) {
    background: rgba(255, 255, 255, 0.85);
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.2);
  }

  .header-left {
    display: flex;
    align-items: center;
    gap: 32px;
  }

  .logo-container {
    font-size: 18px;
    font-weight: bold;
    color: #000;
    display: flex;
    align-items: center;

    img {
      height: 22px;
      margin-right: 8px;
    }
  }

  .header-center {
    flex: 1;
    display: flex;
    justify-content: start;
    padding-left: 13.8vw;
  }

  .nav-link {
    color: #000;
    font-size: 14px;
    cursor: pointer;
    text-decoration: none;
    display: inline-block;
    white-space: nowrap;

    &.active {
      color: #4074dc;

      span {
        border-bottom: 2px solid #4074dc;
      }
    }

    span {
      padding: 4px 0;
    }
  }

  .login-button {
    width: 100px;
    height: 36px;
    flex-shrink: 0;
    border-radius: 24px;
    border: 1px solid #4074dc;
    background: rgba(255, 255, 255, 0);
    backdrop-filter: blur(2px);
    color: #4074dc;
    font-size: 14px;
    display: flex;
    align-items: center;
    justify-content: center;
    text-decoration: none;
    margin-right: 25px;
    &:hover {
      background: #4074dc;
      color: #fff;
    }
  }

  .user-avatar {
    cursor: pointer;
    display: flex;
    align-items: center;
    gap: 8px;

    .ant-avatar {
      flex-shrink: 0;
    }

    span {
      line-height: 1;
      display: flex;
      align-items: center;
    }

    .anticon {
      display: flex;
      align-items: center;
    }
  }
}
