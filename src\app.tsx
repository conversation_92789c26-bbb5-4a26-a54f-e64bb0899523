import type { RunTimeLayoutConfig } from '@umijs/max';
import { history } from '@umijs/max';
import defaultSettings from '../config/defaultSettings';
import { errorConfig } from './requestErrorConfig';
import React, { useState, useEffect, useCallback, useMemo } from 'react';
import { AvatarDropdown, AvatarName } from './components/RightContent/AvatarDropdown';
import { getLoginUser } from '@/services/DataLoom/userController';
import { Space, Avatar, ConfigProvider, App, Modal } from 'antd';
import { CaretDownOutlined } from '@ant-design/icons';
import { isMobileDevice } from './utils/device';
import './app.less';
import { DndProvider } from 'react-dnd';
import { HTML5Backend } from 'react-dnd-html5-backend';
import { useAccess } from '@umijs/max';
import { throttle } from 'lodash';

const loginPath = '/user/login';

/**
 * @see  https://umijs.org/zh-CN/plugins/plugin-initial-state
 * */
export async function getInitialState(): Promise<{
  currentUser?: API.LoginUserVO;
  settings: any;
}> {
  const fetchUserInfo = async () => {
    try {
      const res = await getLoginUser();
      return res.data;
    } catch (error) {
      history.push(loginPath);
    }
    return undefined;
  };
  // 如果不是登录页面，执行
  const { location } = history;
  if (location.pathname !== loginPath) {
    const currentUser = await fetchUserInfo();
    return {
      settings: defaultSettings,
      currentUser,
    };
  }
  // 如果是登录页面不返回当前登录用户的信息
  return {
    settings: defaultSettings,
  };
}

// ProLayout 支持的api https://procomponents.ant.design/components/layout
export const layout: RunTimeLayoutConfig = ({ initialState }) => {
  const [scrollY, setScrollY] = useState(0);

  // 添加移动设备检测
  useEffect(() => {
    const checkMobile = () => {
      if (isMobileDevice()) {
        history.push('/mobile');
      }
    };

    checkMobile();
  }, []);

  // 使用 throttle 优化滚动监听性能
  const throttledScrollHandler = useCallback(
    throttle((scrollTop: number) => {
      setScrollY(scrollTop);
    }, 16), // 约60fps的更新频率
    [],
  );

  // 监听路由变化和设置滚动监听
  useEffect(() => {
    const handleScroll = (e: Event) => {
      const target = e.target as HTMLElement;
      throttledScrollHandler(target.scrollTop);
    };

    // 获取主内容容器
    const mainContainer = document.querySelector('.welcome-container');
    if (mainContainer) {
      // 设置初始滚动位置
      setScrollY(mainContainer.scrollTop);

      // 添加滚动监听，使用 passive 选项优化性能
      mainContainer.addEventListener('scroll', handleScroll, { passive: true });

      // 清理函数
      return () => {
        mainContainer.removeEventListener('scroll', handleScroll);
        // 取消未执行的 throttle 调用
        throttledScrollHandler.cancel();
      };
    } else {
      // 如果容器不存在，重置滚动位置
      setScrollY(0);
    }
  }, [history.location.pathname, throttledScrollHandler]);

  return {
    avatarProps: {
      src: initialState?.currentUser?.userAvatar,
      title: <AvatarName />,
      render: (_, avatarChildren) => {
        return <AvatarDropdown>{avatarChildren}</AvatarDropdown>;
      },
    },
    waterMarkProps: {
      content: initialState?.currentUser?.userName,
    },
    onPageChange: () => {
      // 页面变化时的处理逻辑
    },
    menuHeaderRender: undefined,
    headerRender: () => {
      const isWelcomePage = history.location.pathname === '/welcome';
      const headerClassName = `header-container ${isWelcomePage ? 'welcome-page' : ''} ${isWelcomePage && scrollY > 50 ? 'scrolled' : ''}`;

      // 安全地获取权限信息
      const access = useAccess();
      const canAdmin = access?.canAdmin || false;

      return (
        <div className={headerClassName}>
          <div className="header-left">
            <div className="logo-container">
              <img src="/assets/iaayxt25.svg" alt="logo" />
            </div>
          </div>
          <div className="header-center">
            <Space size={68}>
              {useMemo(() => {
                // 导航项配置
                const navItems = [
                  { path: '/welcome', label: '首页', exact: true },
                  { path: '/mysql_detail/:id', label: '数据源', exact: false },
                  { path: '/dashboard', label: '仪表盘', exact: false },
                  { path: '/ai_chat', label: '深度问数', exact: true },
                  ...(canAdmin ? [{ path: '/system', label: '系统管理', exact: true }] : []),
                ];

                // 检查导航项是否激活
                const isNavActive = (path: string, exact: boolean) => {
                  if (path === '/ai_chat') {
                    // /ai_chat 或 /task_queue 路由下都高亮"深度问数"
                    return history.location.pathname === '/ai_chat' || history.location.pathname.startsWith('/task_queue');
                  }
                  return exact ? history.location.pathname === path : history.location.pathname.startsWith(path);
                };

                // 处理导航点击
                const handleNavClick = (path: string) => {
                  if (!initialState?.currentUser && path !== '/welcome') {
                    Modal.confirm({
                      title: '请先登录',
                      content: '您需要登录后才能访问此页面。',
                      okText: '去登录',
                      cancelText: '取消',
                      onOk: () => history.push('/user/login'),
                    });
                  } else {
                    history.push(path);
                  }
                };

                return navItems.map(({ path, label, exact }, index) => {
                  const isActive = isNavActive(path, exact);

                  return (
                    <a key={`nav-${path}-${index}`} onClick={() => handleNavClick(path)} className={`nav-link ${isActive ? 'active' : ''}`}>
                      <span>{label}</span>
                    </a>
                  );
                });
              }, [canAdmin, history.location.pathname, initialState?.currentUser])}
            </Space>
          </div>
          <div>
            <Space size={8}>
              {initialState?.currentUser ? (
                <AvatarDropdown>
                  <Space className="user-avatar">
                    <Avatar src={initialState.currentUser.userAvatar} />
                    <span>{initialState.currentUser.userName}</span>
                    <CaretDownOutlined style={{ fontSize: '12px' }} />
                  </Space>
                </AvatarDropdown>
              ) : (
                <a onClick={() => history.push('/user/login')} className="login-button">
                  登录
                </a>
              )}
            </Space>
          </div>
        </div>
      );
    },
    childrenRender: (children) => {
      return (
        <ConfigProvider
          theme={{
            token: {
              colorPrimary: '#2868E7',
            },
          }}
        >
          <DndProvider backend={HTML5Backend}>
            <App>{children}</App>
          </DndProvider>
        </ConfigProvider>
      );
    },
    ...initialState?.settings,
  };
};

/**
 * @name request 配置，可以配置错误处理
 * 它基于 axios 和 ahooks 的 useRequest 提供了一套统一的网络请求和错误处理方案。
 * @doc https://umijs.org/docs/max/request#配置
 */
export const request = {
  ...errorConfig,
};
