import React, { useState } from 'react';
import { Modal, Upload, Form, Input, Radio, Space, message } from 'antd';
import { UploadOutlined } from '@ant-design/icons';
import type { UploadFile } from 'antd/es/upload/interface';
import './index.less';
import { addDatasource } from '@/services/DataLoom/coreDataSourceController';

interface FileUploadModalProps {
  visible: boolean;
  onCancel: () => void;
  onOk: (file: File, values: any) => void;
  operationType?: 'update' | 'upload';
}

const FileUploadModal: React.FC<FileUploadModalProps> = ({ visible, onCancel, onOk, operationType = 'upload' }) => {
  const [form] = Form.useForm();
  const [fileList, setFileList] = useState<UploadFile[]>([]);
  const [loading, setLoading] = useState(false);

  // 文件校验和处理
  const beforeUpload = (file: UploadFile & { originFileObj?: File }) => {
    const isCSV = file.type === 'text/csv' || file.name.endsWith('.csv');
    const isXLS = file.type === 'application/vnd.ms-excel' || file.name.endsWith('.xls');
    const isXLSX = file.type === 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet' || file.name.endsWith('.xlsx');
    const isZIP = file.type === 'application/zip' || file.name.endsWith('.zip');
    if (!isCSV && !isXLS && !isXLSX && !isZIP) {
      message.error('仅支持CSV、XLS、XLSX和ZIP文件格式！');
      return false;
    }
    const isLt10M = (file.size || 0) / 1024 / 1024 < 10;
    if (!isLt10M) {
      message.error('文件必须小于10M！');
      return false;
    }
    setFileList([file]);

    // 自动填充数据源名称和文件名称（去掉扩展名）
    const fileName = file.name.replace(/\.[^/.]+$/, '');
    form.setFieldsValue({
      fileName: file.name, // 文件名称（仅显示用）
    });

    return false; // 阻止自动上传
  };

  // 删除上传文件
  const handleRemove = () => {
    setFileList([]);
    // 清空相关字段
    form.setFieldsValue({
      fileName: '',
    });
  };

  // 点击确认
  const handleSubmit = async () => {
    try {
      const values = await form.validateFields();

      if (!fileList.length) {
        message.error('请上传文件！');
        return;
      }

      const file = fileList[0];
      const formData = new FormData();
      formData.append('file', file as any);

      const param = {
        pid: 0,
        name: values.name, // 使用数据源名称
        type: 'excel',
        configuration: '',
        description: values.description,
        file: file,
      };

      const datasourceDTO = JSON.stringify(param);
      formData.append('datasourceDTO', new Blob([datasourceDTO], { type: 'application/json' }));

      setLoading(true);
      try {
        const res = await addDatasource(formData);
        if (res.code === 0) {
          message.success(res.message);
          onOk(file as any, values);
          form.resetFields();
          setFileList([]);
          onCancel();
        } else {
          message.error(res.message || '提交失败');
        }
      } catch (error: any) {
        message.error(error.message || '提交失败，请重试');
      } finally {
        setLoading(false);
      }
    } catch (error) {
      // 表单验证失败
    }
  };

  // 关闭时清理状态
  const handleCancel = () => {
    form.resetFields();
    setFileList([]);
    onCancel();
  };

  return (
    <Modal
      title={operationType === 'update' ? '更新数据' : '文件上传'}
      open={visible}
      onCancel={handleCancel}
      onOk={handleSubmit}
      width={893}
      className="file-upload-modal common-modal"
      okText="确认"
      cancelText="取消"
      okButtonProps={{ disabled: fileList.length === 0, loading }}
      destroyOnHidden
    >
      <Form form={form} layout="vertical" className="upload-form">
        <div className="upload-container">
          <Upload.Dragger
            fileList={fileList}
            beforeUpload={beforeUpload}
            onRemove={handleRemove}
            maxCount={1}
            accept=".csv,.xls,.xlsx,.zip"
            className="upload-dragger"
            showUploadList={{
              showRemoveIcon: true,
              showPreviewIcon: false,
              showDownloadIcon: false,
            }}
          >
            <div className="upload-content">
              <img src="/assets/2thshumk.svg" alt="上传" className="upload-icon" />
              <p className="upload-text">点击或拖拽上传文件</p>
              <p className="upload-desc">(仅支持单个文件上传,大小不超过10M)</p>
              <p className="upload-format">支持的文件格式：CSV（.csv）、XLS（.xls）、XLSX（.xlsx）、ZIP（.zip）</p>
            </div>
          </Upload.Dragger>
        </div>

        <Form.Item name="description" label="文件描述">
          <Input.TextArea placeholder="描述数据集的来源、作用,可不填写" rows={4} />
        </Form.Item>

        <Form.Item name="fileName" label="文件名称">
          <Input placeholder="文件名称" disabled />
        </Form.Item>

        <Form.Item name="name" label="数据源名称" rules={[{ required: true, message: '请输入数据源名称' }]}>
          <Input placeholder="请输入数据源名称" />
        </Form.Item>

        {operationType === 'update' && (
          <Form.Item
            name="updateMode"
            label="更新方式"
            initialValue="append"
            rules={[{ required: true, message: '请选择更新方式' }]}
            className="update-mode"
          >
            <Radio.Group>
              <Space direction="horizontal" size={16}>
                <Radio value="append">新增数据集或在已有数据集表上追加数据</Radio>
                <Radio value="clear">清空并新增数据表</Radio>
              </Space>
            </Radio.Group>
          </Form.Item>
        )}
      </Form>
    </Modal>
  );
};

export default FileUploadModal;
